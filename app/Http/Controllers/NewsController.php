<?php

namespace App\Http\Controllers;

use App\News;

class NewsController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $importantNews = News::where('important', '=', '1')
            ->published()
            ->orderBy('published_at', 'DESC')
            ->first();

        $news = News::with(['images'])
            ->published();

        if ($importantNews) { // exclude important news if exists
            $news->where('id', '!=', $importantNews->id);
        }

        $news = $news->orderBy('published_at', 'DESC')
            ->paginate(6);

        return view('cs.news.index', compact(['news', 'importantNews']));
    }


    /**
     * Display a listing of the resource.
     *
     * @param string $slug
     * @return \Illuminate\Http\Response
     */
    public function show($slug)
    {

        $item = News::with(['images', 'files', 'editor'])
            ->published()
            ->where('slug', '=', $slug)
            ->firstOrFail();

        $otherNews = News::with(['images'])
            ->published()
            ->where('id', '!=', $item->id)
            ->orderBy('published_at', 'DESC')
            ->limit(6)
            ->get();

        return view('cs.news.show', compact(['item', 'otherNews']));
    }

    public static function dayName($day)
    {
        static $names = array('neděle', 'pondělí', 'úterý', 'středa', 'čtvrtek', 'pátek', 'sobota');
        return $names[$day];
    }

}
