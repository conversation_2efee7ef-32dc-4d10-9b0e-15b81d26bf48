<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\News;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;
use Spatie\LaravelImageOptimizer\Facades\ImageOptimizer;


class NewsController extends Controller
{

    /**
     * Create a new AuthController instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth:api');
    }


    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return LengthAwarePaginator
     */
    public function index(Request $request)
    {


        /*sleep(2);*/
        $page = News::with('editor');

        if ($request->q) {
            $page->where('title', 'LIKE', "%{$request->q}%")
                ->orWhere('content', 'LIKE', "%{$request->q}%");
        }

        return $page->orderBy('id', 'desc')->paginate(10);

    }


    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return Response
     */
    public function show($id)
    {
        /*sleep(2);*/
        $page = News::with('editor')->findOrFail($id);
        $page['images'] = $page->images()->orderBy('images.order')->get();
        $page['files'] = $page->files()->orderBy('files.order')->get();

        return response()->json($page, Response::HTTP_OK);
    }


    /**
     * Create the specified resource in storage.
     *
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {

        /*sleep(1);*/

        $request->validate([
            'title' => 'required',
            'published_at' => 'nullable|date',
            /*'content' => 'required',*/
        ]);


        $page = new News(request()->all());
        $page->save();


        /*
         * IMAGES
         * */
        $this->storeImages($page, $request);

        /*
         * FILES
         * */
        $this->storeFiles($page, $request);

        $response = News::with('editor')->findOrFail($page->id);
        $response['images'] = $page->images()->orderBy('order')->get();
        $response['files'] = $page->files()->orderBy('order')->get();


        return response()->json($response, Response::HTTP_OK);

        // TODO: response code
        /*To get something from the API mostly 200 OK is the default status code, which is fine. When a new record is created successful, you want to use 201 Created and for updated records one would exepct a 202 Accepted .*/
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(Request $request, $id)
    {

        $request->validate([
            'title' => 'required',
            'published_at' => 'nullable|date',
        ]);


        $page = News::findOrFail($id);

        $page->fill(request()->all())
            ->save();


        /*
         * IMAGES
         * */
        $this->storeImages($page, $request);

        /*
         * FILES
         * */
        $this->storeFiles($page, $request);


        $response = News::with('editor')->findOrFail($id);
        $response['images'] = $page->images()->orderBy('order')->get();
        $response['files'] = $page->files()->orderBy('order')->get();

        return response()->json($response, Response::HTTP_OK);

        // TODO: response code
        /*To get something from the API mostly 200 OK is the default status code, which is fine. When a new record is created successful, you want to use 201 Created and for updated records one would exepct a 202 Accepted .*/
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return Response
     */
    public function destroy($id)
    {
        $page = News::findOrFail($id);

        $this->deleteFiles($page->images()->get());
        $this->deleteFiles($page->files()->get());

        $page->delete();


    }


    protected function storeImages($page, $request)
    {

        $images = $request->images ?? array();

        // DELETE
        $itemsToKeep = array_filter(Arr::pluck($images, 'id')); // remove null values with array filter
        $imagesToDelete = $page->images()
            ->whereNotIn('id', $itemsToKeep);

        $this->deleteFiles($imagesToDelete->get());


        $imagesToDelete->delete();


        // UPDATE + CREATE
        $files = $request->file('images');
        foreach ($images as $key => $image) {

            $data = array(
                'name' => $image['name'],
                'order' => $key
            );

            if (isset($files[$key])) { // Actual only for new images
                $file = $files[$key]['fileData'];
                $data['file'] = $file->store('images', 'public');
                $data['type'] = $file->getClientMimeType();
                $data['size'] = $file->getSize();

                $imagePath = public_path('storage/' . $data['file']);
                Image::make($file)->orientate()->fit(800, 800)->save($imagePath, 30);

                ImageOptimizer::optimize($imagePath); // replace with optimized version
            }

            if (isset($image['id'])) {
                $page->images()->where('id', $image['id'])->update($data);
            } else {
                $page->images()->create($data);
            }
        }

    }


    protected function storeFiles($page, $request)
    {

        $files = $request->input('files') ?? array();

        // DELETE
        $itemsToKeep = array_filter(Arr::pluck($files, 'id')); // remove null values with array filter
        $filesToDelete = $page->files()
            ->whereNotIn('id', $itemsToKeep);

        $this->deleteFiles($filesToDelete->get());


        $filesToDelete->delete();


        // UPDATE + CREATE
        $filesData = $request->file('files');


        foreach ($files as $key => $image) {

            $data = array(
                'name' => $image['name'],
                'order' => $key
            );

            if (isset($filesData[$key])) { // Actual only for new files
                $file = $filesData[$key]['fileData'];
                $data['file'] = $file->store('files', 'public');
                $data['type'] = $file->getClientMimeType();
                $data['size'] = $file->getSize();
            }

            if (isset($image['id'])) {
                $page->files()->where('id', $image['id'])->update($data);
            } else {
                $page->files()->create($data);
            }
        }

    }


    protected function deleteFiles($images)
    {
        foreach ($images as $image) {
            Storage::disk('public')->delete($image->file);
        }
    }


}
