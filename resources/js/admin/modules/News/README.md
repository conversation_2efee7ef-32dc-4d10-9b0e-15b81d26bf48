# News Module - Scheduled Publication

## Overview

News module with scheduled publication functionality. Users can either publish articles immediately or schedule them for future publication.

## Features

- **Immediate Publication**: Articles are published immediately when created/updated
- **Scheduled Publication**: Articles can be scheduled for future publication
- **Timezone Handling**: Proper timezone conversion between UI and database

## How It Works

### UI Components
- Checkbox: "Naplánovat publikování" (Schedule Publication)
- DateTime picker: Appears when scheduling is enabled
- Validation: Future dates only for new articles

### Data Flow
1. **User Input**: Local time in datetime-local input
2. **Frontend**: Sends datetime as ISO string to Laravel
3. **<PERSON>vel**: Automatically converts to UTC and stores in database
4. **Display**: <PERSON><PERSON> returns datetime in app timezone (Europe/Prague), frontend preserves user input

### Key Functions

#### `utcToLocalInput(laravelString)`
Converts Laravel datetime response to local browser time for UI display.
- <PERSON><PERSON> returns datetime in app timezone (Europe/Prague) as ISO string
- JavaScript Date constructor automatically handles timezone parsing
- Returns format suitable for datetime-local input (YYYY-MM-DDTHH:MM)
- Used only when loading existing data, not after saves

#### `localInputToUtc(localInput)`
Converts local datetime input for server submission.
- Converts datetime-local input to ISO string
- Laravel automatically handles timezone conversion from user's timezone to UTC
- No manual timezone manipulation needed

### User Input Preservation
- When scheduling is enabled, user's original input is preserved after save
- This prevents the datetime from appearing "modified" after save operations
- Only when loading existing data does the component convert from Laravel's format

## Usage

### Creating Scheduled Article
1. Check "Naplánovat publikování"
2. Select future date/time
3. Save article
4. Article won't appear on frontend until scheduled time

### Editing Existing Article
1. Checkbox automatically checked if article has publication date
2. DateTime picker shows current publication time
3. Can modify or disable scheduling

## Technical Notes

- Database stores all times in UTC (Laravel handles this automatically)
- Laravel timezone is set to 'Europe/Prague'
- Laravel automatically converts between UTC (database) and app timezone
- Frontend sends datetime as ISO string, Laravel handles timezone conversion
- No manual timezone manipulation in Vue component
- UI state preserved after save operations
- Proper DST (Daylight Saving Time) handling through native JavaScript and Laravel

## Status Indicators (Admin List)

- **Published** (green): Article is live on frontend
- **Scheduled** (yellow): Article will be published in future
- **Draft** (gray): Article has no publication date

## Frontend Filtering

All frontend controllers use `published()` scope to only show articles where `published_at <= now()`.
