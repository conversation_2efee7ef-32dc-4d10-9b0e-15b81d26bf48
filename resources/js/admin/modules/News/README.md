# News Module - Scheduled Publication

## Overview

News module with scheduled publication functionality. Users can either publish articles immediately or schedule them for future publication.

## Features

- **Immediate Publication**: Articles are published immediately when created/updated
- **Scheduled Publication**: Articles can be scheduled for future publication
- **Timezone Handling**: Proper timezone conversion between UI and database

## How It Works

### UI Components
- Checkbox: "Naplánovat publikování" (Schedule Publication)
- DateTime picker: Appears when scheduling is enabled
- Validation: Future dates only for new articles

### Data Flow
1. **User Input**: Local time in datetime-local input
2. **Frontend**: Converts to UTC before sending to server
3. **Laravel**: Stores UTC time in database
4. **Display**: Converts back to local time for UI

### Key Functions

#### `utcToLocalInput(laravelString)`
Converts Laravel datetime response to local time for UI display.
- Automatically detects timezone offset (handles DST correctly)
- Uses JavaScript's getTimezoneOffset() for accurate conversion
- Returns format suitable for datetime-local input

#### `localInputToUtc(localInput)`
Converts local datetime input to UTC for server.
- Uses JavaScript's built-in timezone conversion
- Returns ISO string format

## Usage

### Creating Scheduled Article
1. Check "Naplánovat publikování"
2. Select future date/time
3. Save article
4. Article won't appear on frontend until scheduled time

### Editing Existing Article
1. Checkbox automatically checked if article has publication date
2. DateTime picker shows current publication time
3. Can modify or disable scheduling

## Technical Notes

- Database stores all times in UTC
- Laravel timezone is set to 'Europe/Prague'
- Frontend handles timezone conversion automatically
- UI state preserved after save operations

## Status Indicators (Admin List)

- **Published** (green): Article is live on frontend
- **Scheduled** (yellow): Article will be published in future
- **Draft** (gray): Article has no publication date

## Frontend Filtering

All frontend controllers use `published()` scope to only show articles where `published_at <= now()`.
