<template>

    <container class="uk-padding">


        <!--<a href="#" class="uk-button uk-button-link uk-icon tm-back"><span uk-icon="icon: chevron-left; "></span>Back</a>-->

        <div class="uk-child-width-expand@s uk-flex-top uk-grid">
            <div>

                <div class="uk-flex uk-flex-wrap ">
                    <div class="uk-width-1-1">
                        <h2 class="uk-h3">Spravovat novinky</h2>
                    </div>
                    <!--<div class="uk-width-1-1">
                        <ul class="uk-breadcrumb">
                            <li><a href="#">Home</a></li>
                            <li class="uk-disabled"><a href="#">Articles</a></li>
                        </ul>
                    </div>-->
                </div>
            </div>
            <br/>
            <br/>
            <div>

            </div>
        </div>


        <div class="uk-child-width-expand@s uk-margin-small-top uk-flex-middle" uk-grid>


            <div class="uk-width-expand">
                <div class="uk-child-width-expand@s" uk-grid>

                    <div class="uk-width-expand">
                        <form class="uk-search uk-search-default uk-width-5-6">
                            <span uk-search-icon></span>
                            <input class="uk-search-input uk-flex-wrap-stretch" id="search" type="search"
                                   autocomplete="off"
                                   @keyup="search($event.target.value)"
                                   placeholder="Search...">
                        </form>
                    </div>
                </div>

            </div>

            <div class="uk-width-auto">


                <router-link class="uk-button uk-button-primary uk-icon uk-align-right" to="/admin/news/add"><span
                    uk-icon="icon: plus; "></span> Přidat novinku
                </router-link>
            </div>


        </div>

        <hr />


        <div class="uk-container  uk-container-expand uk-padding uk-padding-remove-top uk-position-relative">


            <table class="uk-table uk-table-middle uk-table-divider-- uk-table-striped">
                <thead>
                <tr>
                    <th>Titulek</th>
                    <th>Slug</th>
                    <th>Status</th>
                    <th>Publikováno</th>
                    <th class="uk-width-small">Možnosti</th>
                </tr>
                </thead>

                <table-body :rows="page.data">
                    <template slot-scope="row">
                        <td>
                            <router-link
                                :to="{
                                            name: 'news.add-edit',
                                            params: { id: row.id },
                                        }"
                            >
                                {{ row.title }}
                            </router-link>
                            <br/>
                            {{ row.content | striphtml | truncate(100) }}
                        </td>
                        <td>{{ row.slug }}</td>
                        <td>
                            <span v-if="getPublicationStatus(row) === 'published'" class="uk-label uk-label-success">Publikováno</span>
                            <span v-else-if="getPublicationStatus(row) === 'scheduled'" class="uk-label uk-label-warning">Naplánováno</span>
                            <span v-else class="uk-label">Koncept</span>
                        </td>
                        <td>
                            <span v-if="row.published_at">{{ formatDate(row.published_at) }}</span>
                            <span v-else class="uk-text-muted">—</span>
                        </td>
                        <td>

                            <button :disabled="!row.id" @click.prevent="deletePage(row.id)" class="uk-button uk-button-danger" type="submit">
                                <span uk-icon="icon: trash;"></span>
                            </button>

                        </td>
                    </template>
                </table-body>
            </table>


            <pagination :data="page" @pagination-change-page="setPage" class="uk-pagination">
                <span slot="prev-nav">&lt; Předchozí</span>
                <span slot="next-nav">Další &gt;</span>
            </pagination>
        </div>


    </container>
</template>

<script>
    import Card from "../../components/Card";
    import Container from "../../components/Container";
    import TableBody from "../../components/TableBody";
    import {RepositoryFactory} from "../../repositories/RepositoryFactory";

    const NewsRepository = RepositoryFactory.get("news");

    export default {
        name: "PageList",
        components: {
            Container,
            Card,
            TableBody
        },
        data() {
            return {
                page: {},
            };
        },
        created() {
            this.fetch();
        },
        route: {
            data ({ to }) {
                console.log("route data to")
            }
        },
        methods: {
            async fetch() {
                const {data} = await NewsRepository.get({page: this.$route.query.page});
                this.page = data;
            },
            async search(q) {

                const {data} = await NewsRepository.get({q: q});
                this.page = data;
            },
            setPage(page) {
                this.$router.push({name: 'news', query: {'page': page}});
            },
            async deletePage(id) {

                const {data} = await NewsRepository.delete(id);

                UIkit.notification({message: "Smazáno", status: 'success'});

                this.$router.push({name: 'news'});
                this.$router.go();

            },

            getPublicationStatus(row) {
                if (!row.published_at) {
                    return 'draft';
                }

                const publishedDate = new Date(row.published_at);
                const now = new Date();

                if (publishedDate <= now) {
                    return 'published';
                } else {
                    return 'scheduled';
                }
            },

            formatDate(dateString) {
                if (!dateString) return '';

                const date = new Date(dateString);
                const day = String(date.getDate()).padStart(2, '0');
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const year = date.getFullYear();
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');

                return `${day}.${month}.${year} ${hours}:${minutes}`;
            }

        },
    };
</script>

<style></style>
