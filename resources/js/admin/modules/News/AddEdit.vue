<template>
    <container class="uk-position-relative">

        <div class="uk-padding">


            <div class="uk-child-width-expand@s uk-flex-top uk-grid">
                <div>

                    <div class="uk-flex uk-flex-wrap ">
                        <div class="uk-width-1-1">
                            <h2 class="uk-h3">Edit<PERSON> novin<PERSON></h2>
                        </div>
                        <!--<div class="uk-width-1-1">
                            <ul class="uk-breadcrumb">
                                <li><a href="#">Home</a></li>
                                <li class="uk-disabled"><a href="#">Articles</a></li>
                            </ul>
                        </div>-->
                    </div>
                </div>
                <br/>
                <br/>
                <div>

                </div>
            </div>


            <div class="uk-child-width-expand@s uk-margin-small-top uk-flex-middle" uk-grid>


                <div class="uk-width-expand">
                    <div class="uk-child-width-expand@s uk-flex uk-flex-middle" uk-grid>

                        <div class="uk-width-expand">
                            <div v-if="form.id">
                                Naposledy ulo<PERSON> {{ form.updated_at | moment('calendar') }} uživatelem {{ form.editor.name }}
                            </div>

                        </div>

                    </div>

                </div>

                <div class="uk-width-auto">


                    <save-button class="" @save="save"></save-button>

                    <button :disabled="!form.id" @click.prevent="deletePage(form.id)" class="uk-button uk-button-danger" type="submit">
                        <span uk-icon="icon: trash;"></span>
                    </button>

                </div>


            </div>

            <hr/>
        </div>


            <div class="uk-padding uk-padding-remove-top">
                <form class="uk-form-stacked" enctype="multipart/form-data" method="post" name="1"
                      v-on:submit.prevent>

                    <div class="uk-margin">
                        <label class="uk-form-label" for="form-stacked-text">Titulek</label>
                        <div class="uk-form-controls">
                            <input class="uk-input uk-form-large" id="form-stacked-text" name="title" placeholder="Zadejte titulek stránky..."
                                   type="text"
                                   v-model="form.title" value="">
                        </div>
                    </div>



                    <div class="uk-margin">
                        <label class="uk-form-label" for="content">Text novinky</label>
                        <div class="uk-form-controls">
                            <vue-editor
                                id="content"
                                :editorOptions="editorSettings"
                                v-model="form.content" />
                        </div>
                    </div>



                    <div class="uk-margin uk-margin-large-bottom">
                        <label class="uk-form-label" for="important">Úvodní stránka</label>
                        <div class="uk-form-controls">
                            <input class="uk-checkbox" id="important" name="important" placeholder="Zadejte titulek stránky..."
                                   type="checkbox"
                                   v-bind:true-value="1"
                                   v-bind:false-value="0"
                                   v-model="form.important" value=""> Jedná se o důležitou zprávu
                        </div>
                    </div>

                    <div class="uk-margin uk-margin-large-bottom">
                        <label class="uk-form-label" for="schedule_publication">Publikování</label>
                        <div class="uk-form-controls">
                            <input class="uk-checkbox" id="schedule_publication" name="schedule_publication"
                                   type="checkbox"
                                   v-model="schedulePublication" value=""> Naplánovat publikování
                        </div>

                        <div v-if="schedulePublication" class="uk-margin-top">
                            <label class="uk-form-label" for="published_at">Datum a čas publikování</label>
                            <div class="uk-form-controls">
                                <input class="uk-input" id="published_at" name="published_at"
                                       type="datetime-local"
                                       v-model="publishedAtInput">
                            </div>
                            <div class="uk-text-meta uk-margin-small-top">
                                Zadejte datum a čas publikování.
                            </div>
                        </div>
                    </div>



                    <image-uploader :images="form.images" @updateList="updateImageUploader"></image-uploader>

                    <file-uploader :files="form.files" @updateFileList="updateFileUploader"></file-uploader>

                    <save-button @save="save"></save-button>


                </form>

            </div>


        <loading/>

    </container>
</template>

<script>
    import Card from "../../components/Card";
    import Container from "../../components/Container";
    import ImageUploader from "../../components/ImageUploader";
    import FileUploader from "../../components/FileUploader";
    import Editor from "../../components/Editor";
    import Loading from "../../components/Loading";
    import SaveButton from "../../components/SaveButton";
    import {RepositoryFactory} from '../../repositories/RepositoryFactory';

    // Basic Use - Covers most scenarios
    import { VueEditor, Quill } from "vue2-editor";

    import QuillPasteSmart from 'quill-paste-smart';
    Quill.register('modules/clipboard', QuillPasteSmart, true);

    import htmlEditButton from 'quill-html-edit-button';
    Quill.register("modules/htmlEditButton", htmlEditButton);





    const NewsRepository = RepositoryFactory.get('news');

    export default {
        name: "NewsAddEdit",
        data() {
            return {
                id: null,
                form: {},
                schedulePublication: false,
                publishedAtInput: '',
                editorSettings: {
                    modules: {
                        clipboard: {
                            allowed: {
                                tags: ['a', 'b', 'strong', 'u', 's', 'i', 'p', 'br', 'ul', 'ol', 'li', 'span', 'div', 'table', 'tr', 'td'],
                                attributes: ['class', 'href', 'rel', 'target']
                            },
                            keepSelection: true,
                        },
                    }
                },
            };
        },
        components: {
            Container,
            Card,
            ImageUploader,
            FileUploader,
            Editor,
            Loading,
            SaveButton,
            VueEditor, // html !!! https://github.com/quilljs/quill/issues/128
        },
        created() {
            this.id = this.$route.params.id;
            this.fetch();
        },
        methods: {
            utcToLocalInput(laravelString) {
                if (!laravelString) return '';

                // Laravel returns datetime in app timezone (Europe/Prague)
                // We need to convert it to local browser time for datetime-local input
                const serverDate = new Date(laravelString);

                // Get the local timezone offset in minutes
                const timezoneOffset = serverDate.getTimezoneOffset();

                // Adjust for local timezone
                const localDate = new Date(serverDate.getTime() - (timezoneOffset * 60 * 1000));

                const year = localDate.getFullYear();
                const month = String(localDate.getMonth() + 1).padStart(2, '0');
                const day = String(localDate.getDate()).padStart(2, '0');
                const hours = String(localDate.getHours()).padStart(2, '0');
                const minutes = String(localDate.getMinutes()).padStart(2, '0');

                return `${year}-${month}-${day}T${hours}:${minutes}`;
            },

            localInputToUtc(localInput) {
                if (!localInput) return null;
                // Let Laravel handle timezone conversion automatically
                // Just send the datetime as ISO string
                return new Date(localInput).toISOString();
            },

            updateImageUploader(e) {
                this.form.images = e;
            },

            updateFileUploader(e) {
                this.form.files = e;
            },

            async fetch() {
                if (!this.id) {
                    this.form = {
                        images: [],
                        files: [],
                    };
                    this.publishedAtInput = '';
                    this.schedulePublication = false;
                    return;
                }

                const {data} = await NewsRepository.getPage(this.id);
                this.form = data;

                if (this.form.published_at) {
                    this.publishedAtInput = this.utcToLocalInput(this.form.published_at);
                    this.schedulePublication = true;
                } else {
                    this.publishedAtInput = '';
                    this.schedulePublication = false;
                }
            },

            async save() {
                const formData = {...this.form};

                if (this.schedulePublication && this.publishedAtInput) {
                    formData.published_at = this.localInputToUtc(this.publishedAtInput);
                } else {
                    formData.published_at = new Date().toISOString();
                }

                try {
                    if (this.id) {
                        const {data} = await NewsRepository.update(this.id, formData);
                        this.form = data;
                    } else {
                        const {data} = await NewsRepository.create(formData);
                        this.id = data.id;
                        this.form = data;
                        this.$router.push({name: 'news.add-edit', params: {'id': data.id}});
                    }

                    UIkit.notification({message: "Uloženo", status: 'success'});
                } catch (error) {
                    UIkit.notification({message: "Chyba při ukládání", status: 'danger'});
                }
            },
            async deletePage(id) {

                const {data} = await NewsRepository.delete(id);

                UIkit.notification({message: "Smazáno", status: 'success'});

                this.$router.push({name: 'news'});

            }


        }
    };
</script>

<style>
    #editor2 .ql-editor {
        min-height: 40px;
    }
</style>
